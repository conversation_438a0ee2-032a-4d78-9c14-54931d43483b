# Copyright (C) 2024 Tongsuo Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Tongsuo library integration for Trusty TEE
# This file integrates Tongsuo using its native build system with namespace isolation

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

# Module dependencies
MODULE_DEPS += \
    user/base/lib/rng

# Use Tongsuo's native build system
# Configure for Trusty environment with SM2/SM3/SM4 support
TONGSUO_CONFIG_ARGS := \
    linux-aarch64 \
    --prefix=/tmp/tongsuo-trusty \
    --openssldir=/tmp/tongsuo-trusty/ssl \
    no-shared \
    no-dso \
    no-engine \
    no-async \
    no-sock \
    no-dgram \
    no-stdio \
    no-autoload-config \
    no-autoerrinit \
    no-autoalginit \
    no-comp \
    no-err \
    no-filenames \
    no-posix-io \
    no-ui-console \
    no-tests \
    no-fuzz-afl \
    no-fuzz-libfuzzer \
    no-external-tests \
    no-buildtest-c++ \
    enable-sm2 \
    enable-sm3 \
    enable-sm4 \
    enable-zuc \
    enable-ntls

# Configure and build Tongsuo using its native build system
$(LOCAL_DIR)/configdata.pm: $(LOCAL_DIR)/Configure
	cd $(LOCAL_DIR) && ./Configure $(TONGSUO_CONFIG_ARGS)

$(LOCAL_DIR)/libcrypto.a: $(LOCAL_DIR)/configdata.pm
	cd $(LOCAL_DIR) && $(MAKE) libcrypto.a

$(LOCAL_DIR)/libssl.a: $(LOCAL_DIR)/configdata.pm
	cd $(LOCAL_DIR) && $(MAKE) libssl.a

# Create combined library
MODULE_LIBRARY := $(LOCAL_DIR)/libtongsuo.a

$(MODULE_LIBRARY): $(LOCAL_DIR)/libcrypto.a $(LOCAL_DIR)/libssl.a
	$(AR) rcs $@ $(LOCAL_DIR)/libcrypto.a $(LOCAL_DIR)/libssl.a

# Export library for linking
MODULE_EXPORT_LIBRARY := $(MODULE_LIBRARY)

# IMPORTANT: Namespace isolation
# Tongsuo headers are NOT exported globally to avoid conflicts with BoringSSL
# Only modules that explicitly depend on Tongsuo will include its headers
# This is achieved by NOT setting MODULE_EXPORT_INCLUDES

# Clean target
.PHONY: tongsuo-clean
tongsuo-clean:
	cd $(LOCAL_DIR) && $(MAKE) clean
	rm -f $(LOCAL_DIR)/configdata.pm $(MODULE_LIBRARY)

include make/rctee_lib.mk
